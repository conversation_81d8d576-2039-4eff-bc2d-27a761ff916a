package com.goat.material.pojo.vo.videocomposition;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 视频合成响应
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Accessors(chain = true)
public class VideoCompositionResponse {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务状态 (pending, processing, completed, failed)
     */
    private String status;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 输出视频文件路径
     */
    private String outputVideoPath;

    /**
     * 输出视频URL
     */
    private String outputVideoUrl;

    /**
     * 缩略图路径
     */
    private String thumbnailPath;

    /**
     * 缩略图URL
     */
    private String thumbnailUrl;

    /**
     * 预览视频路径
     */
    private String previewVideoPath;

    /**
     * 预览视频URL
     */
    private String previewVideoUrl;

    /**
     * 字幕文件路径
     */
    private String subtitlePath;

    /**
     * 字幕文件URL
     */
    private String subtitleUrl;

    /**
     * 配音文件路径
     */
    private String voiceoverPath;

    /**
     * 配音文件URL
     */
    private String voiceoverUrl;

    /**
     * 视频时长（秒）
     */
    private Float duration;

    /**
     * 视频宽度
     */
    private Integer width;

    /**
     * 视频高度
     */
    private Integer height;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件MD5哈希
     */
    private String md5Hash;

    /**
     * 处理进度 (0-100)
     */
    private Integer progress;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 处理日志
     */
    private List<String> processLogs;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 开始处理时间
     */
    private Date startedAt;

    /**
     * 完成时间
     */
    private Date completedAt;

    /**
     * 创建者ID
     */
    private Integer creatorId;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 估计剩余时间（秒）
     */
    private Integer estimatedTimeRemaining;

    /**
     * 处理统计信息
     */
    private ProcessingStats processingStats;

    @Data
    @Accessors(chain = true)
    public static class ProcessingStats {
        /**
         * 总处理时间（秒）
         */
        private Integer totalProcessingTime;

        /**
         * 配音生成时间（秒）
         */
        private Integer voiceoverGenerationTime;

        /**
         * 字幕生成时间（秒）
         */
        private Integer subtitleGenerationTime;

        /**
         * 视频合成时间（秒）
         */
        private Integer videoCompositionTime;

        /**
         * 上传时间（秒）
         */
        private Integer uploadTime;

        /**
         * 使用的CPU时间（秒）
         */
        private Integer cpuTime;

        /**
         * 使用的内存峰值（MB）
         */
        private Integer peakMemoryUsage;
    }
}
