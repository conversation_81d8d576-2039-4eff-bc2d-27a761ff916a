package com.goat.material.pojo.req.videocomposition;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 裁剪配置
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Accessors(chain = true)
public class CropConfig {

    /**
     * 是否启用裁剪
     */
    private Boolean enabled = false;

    /**
     * 裁剪起始X坐标
     */
    private Integer x = 0;

    /**
     * 裁剪起始Y坐标
     */
    private Integer y = 0;

    /**
     * 裁剪宽度
     */
    private Integer width;

    /**
     * 裁剪高度
     */
    private Integer height;

    /**
     * 裁剪模式 (exact, fit, fill)
     */
    private String mode = "exact";
}
