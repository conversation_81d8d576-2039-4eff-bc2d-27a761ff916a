package com.goat.material.pojo.req.videocomposition;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 转场效果配置
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Accessors(chain = true)
public class TransitionConfig {

    /**
     * 转场类型 (fade, dissolve, slide, wipe, zoom, rotate)
     */
    private String type = "fade";

    /**
     * 转场时长（秒）
     */
    private Float duration = 1.0f;

    /**
     * 转场开始时间（秒）
     */
    private Float startTime;

    /**
     * 转场方向 (left, right, up, down, center)
     */
    private String direction = "center";

    /**
     * 转场缓动函数 (linear, ease-in, ease-out, ease-in-out)
     */
    private String easing = "ease-in-out";

    /**
     * 转场颜色（用于某些转场效果）
     */
    private String color = "#000000";

    /**
     * 转场强度 (0.0-1.0)
     */
    private Float intensity = 1.0f;

    /**
     * 是否反向转场
     */
    private Boolean reverse = false;
}
