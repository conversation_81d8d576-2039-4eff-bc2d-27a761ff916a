package com.goat.material.pojo.req.videocomposition;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 口播配置
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Accessors(chain = true)
public class TalkingHeadConfig {

    /**
     * 是否启用口播
     */
    private Boolean enabled = false;

    /**
     * 虚拟主播类型 (avatar, real_person, cartoon)
     */
    private String avatarType = "avatar";

    /**
     * 主播模型ID或路径
     */
    private String avatarId;

    /**
     * 主播视频素材路径
     */
    private String avatarVideoPath;

    /**
     * 主播图片路径（静态主播）
     */
    private String avatarImagePath;

    /**
     * 主播位置 (left, center, right, custom)
     */
    private String position = "center";

    /**
     * 自定义位置X坐标（像素）
     */
    private Integer positionX;

    /**
     * 自定义位置Y坐标（像素）
     */
    private Integer positionY;

    /**
     * 主播大小缩放比例 (0.1-2.0)
     */
    private Float scale = 1.0f;

    /**
     * 主播宽度（像素）
     */
    private Integer width;

    /**
     * 主播高度（像素）
     */
    private Integer height;

    /**
     * 是否启用口型同步
     */
    private Boolean lipSyncEnabled = true;

    /**
     * 口型同步精度 (low, medium, high)
     */
    private String lipSyncAccuracy = "medium";

    /**
     * 是否启用表情控制
     */
    private Boolean expressionEnabled = false;

    /**
     * 表情类型 (neutral, smile, serious, surprised)
     */
    private String expressionType = "neutral";

    /**
     * 是否启用手势
     */
    private Boolean gestureEnabled = false;

    /**
     * 手势类型 (none, pointing, waving, explaining)
     */
    private String gestureType = "none";

    /**
     * 主播透明度 (0.0-1.0)
     */
    private Float opacity = 1.0f;

    /**
     * 背景移除 (绿幕抠图)
     */
    private Boolean backgroundRemoval = false;

    /**
     * 绿幕颜色 (十六进制)
     */
    private String chromaKeyColor = "#00FF00";

    /**
     * 绿幕容差 (0.0-1.0)
     */
    private Float chromaKeyTolerance = 0.3f;

    /**
     * 主播出现时间（秒）
     */
    private Float startTime = 0.0f;

    /**
     * 主播结束时间（秒）
     */
    private Float endTime;

    /**
     * 主播淡入时长（秒）
     */
    private Float fadeInDuration = 0.5f;

    /**
     * 主播淡出时长（秒）
     */
    private Float fadeOutDuration = 0.5f;
}
