package com.goat.material.pojo.req.videocomposition;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 字幕配置
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Accessors(chain = true)
public class SubtitleConfig {

    /**
     * 是否启用字幕
     */
    private Boolean enabled = false;

    /**
     * 字幕类型 (embedded, srt, ass)
     */
    private String subtitleType = "embedded";

    /**
     * 字体名称
     */
    private String fontName = "Arial";

    /**
     * 字体大小
     */
    private Integer fontSize = 24;

    /**
     * 字体颜色 (十六进制，如 #FFFFFF)
     */
    private String fontColor = "#FFFFFF";

    /**
     * 字体描边颜色
     */
    private String outlineColor = "#000000";

    /**
     * 字体描边宽度
     */
    private Integer outlineWidth = 2;

    /**
     * 字幕背景颜色 (可选)
     */
    private String backgroundColor;

    /**
     * 字幕背景透明度 (0.0-1.0)
     */
    private Float backgroundOpacity = 0.8f;

    /**
     * 字幕位置 (top, center, bottom)
     */
    private String position = "bottom";

    /**
     * 字幕水平对齐 (left, center, right)
     */
    private String alignment = "center";

    /**
     * 字幕垂直边距（像素）
     */
    private Integer marginVertical = 50;

    /**
     * 字幕水平边距（像素）
     */
    private Integer marginHorizontal = 50;

    /**
     * 每行最大字符数
     */
    private Integer maxCharsPerLine = 40;

    /**
     * 最大行数
     */
    private Integer maxLines = 2;

    /**
     * 字幕显示时长（秒）
     */
    private Float displayDuration = 3.0f;

    /**
     * 字幕淡入时长（秒）
     */
    private Float fadeInDuration = 0.2f;

    /**
     * 字幕淡出时长（秒）
     */
    private Float fadeOutDuration = 0.2f;

    /**
     * 是否自动换行
     */
    private Boolean autoWrap = true;

    /**
     * 字幕与配音同步
     */
    private Boolean syncWithVoiceover = true;

    /**
     * 字幕时间偏移（秒）
     */
    private Float timeOffset = 0.0f;

    /**
     * 是否启用字幕动画
     */
    private Boolean animationEnabled = false;

    /**
     * 动画类型 (slide, fade, typewriter)
     */
    private String animationType = "fade";
}
