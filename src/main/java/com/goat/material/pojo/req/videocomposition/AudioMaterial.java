package com.goat.material.pojo.req.videocomposition;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 音频素材
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Accessors(chain = true)
public class AudioMaterial {

    /**
     * 素材ID
     */
    private String id;

    /**
     * 素材文件路径或URL
     */
    @NotBlank(message = "音频素材路径不能为空")
    private String filePath;

    /**
     * 素材名称
     */
    private String name;

    /**
     * 音频类型 (voiceover, background, effect)
     */
    private String audioType = "background";

    /**
     * 开始时间（秒）
     */
    @NotNull(message = "开始时间不能为空")
    private Float startTime;

    /**
     * 结束时间（秒）
     */
    private Float endTime;

    /**
     * 音频在视频中的开始时间（秒）
     */
    private Float videoStartTime = 0.0f;

    /**
     * 音频持续时长（秒）
     */
    private Float duration;

    /**
     * 音频层级
     */
    private Integer layer = 1;

    /**
     * 音量 (0.0-2.0)
     */
    private Float volume = 1.0f;

    /**
     * 播放速度 (0.25-4.0)
     */
    private Float speed = 1.0f;

    /**
     * 是否循环播放
     */
    private Boolean loop = false;

    /**
     * 淡入时长（秒）
     */
    private Float fadeInDuration = 0.0f;

    /**
     * 淡出时长（秒）
     */
    private Float fadeOutDuration = 0.0f;

    /**
     * 音频滤镜效果
     */
    private String filter;

    /**
     * 左右声道平衡 (-1.0 到 1.0，-1.0为左声道，1.0为右声道)
     */
    private Float pan = 0.0f;

    /**
     * 低音增益 (-20 到 20 dB)
     */
    private Float bassGain = 0.0f;

    /**
     * 高音增益 (-20 到 20 dB)
     */
    private Float trebleGain = 0.0f;

    /**
     * 是否启用降噪
     */
    private Boolean noiseReduction = false;

    /**
     * 降噪强度 (0.0-1.0)
     */
    private Float noiseReductionStrength = 0.5f;

    /**
     * 是否启用音频压缩
     */
    private Boolean compression = false;

    /**
     * 压缩比例 (1:1 到 20:1)
     */
    private Float compressionRatio = 4.0f;

    /**
     * 混合模式 (mix, replace, duck)
     */
    private String mixMode = "mix";
}
