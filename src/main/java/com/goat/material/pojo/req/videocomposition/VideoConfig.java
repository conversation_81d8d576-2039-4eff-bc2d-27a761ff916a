package com.goat.material.pojo.req.videocomposition;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 视频配置
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Accessors(chain = true)
public class VideoConfig {

    /**
     * 视频宽度
     */
    @NotNull(message = "视频宽度不能为空")
    @Min(value = 1, message = "视频宽度必须大于0")
    private Integer width;

    /**
     * 视频高度
     */
    @NotNull(message = "视频高度不能为空")
    @Min(value = 1, message = "视频高度必须大于0")
    private Integer height;

    /**
     * 帧率
     */
    private Integer frameRate = 30;

    /**
     * 视频码率 (kbps)
     */
    private Integer bitrate = 2000;

    /**
     * 视频编码格式
     */
    private String codec = "libx264";

    /**
     * 视频质量 (CRF值，0-51，越小质量越好)
     */
    private Integer quality = 23;

    /**
     * 编码预设 (ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow)
     */
    private String preset = "fast";

    /**
     * 视频总时长（秒）
     */
    private Float duration;

    /**
     * 背景颜色 (十六进制，如 #000000)
     */
    private String backgroundColor = "#000000";

    /**
     * 是否启用硬件加速
     */
    private Boolean hardwareAcceleration = false;

    /**
     * 硬件加速类型 (nvenc, qsv, videotoolbox)
     */
    private String hardwareAccelerationType;
}
