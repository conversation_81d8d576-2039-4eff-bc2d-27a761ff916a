package com.goat.material.pojo.req.videocomposition;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 配音配置
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Accessors(chain = true)
public class VoiceoverConfig {

    /**
     * 是否启用配音
     */
    private Boolean enabled = false;

    /**
     * TTS引擎类型 (ali, elevenlabs)
     */
    private String ttsEngine = "ali";

    /**
     * 配音演员/声音ID
     */
    private String voiceId;

    /**
     * 语言代码 (zh-CN, en-US, etc.)
     */
    private String language = "zh-CN";

    /**
     * 语速 (0.5-2.0)
     */
    private Float speed = 1.0f;

    /**
     * 音量 (0.0-2.0)
     */
    private Float volume = 1.0f;

    /**
     * 音调 (0.5-2.0)
     */
    private Float pitch = 1.0f;

    /**
     * 音频格式
     */
    private String audioFormat = "mp3";

    /**
     * 音频采样率
     */
    private Integer sampleRate = 44100;

    /**
     * 音频码率 (kbps)
     */
    private Integer audioBitrate = 128;

    /**
     * 是否添加停顿
     */
    private Boolean addPauses = true;

    /**
     * 句子间停顿时长（毫秒）
     */
    private Integer sentencePause = 500;

    /**
     * 段落间停顿时长（毫秒）
     */
    private Integer paragraphPause = 1000;

    /**
     * 配音开始时间偏移（秒）
     */
    private Float startOffset = 0.0f;

    /**
     * 是否启用情感表达
     */
    private Boolean emotionEnabled = false;

    /**
     * 情感类型 (neutral, happy, sad, angry, excited)
     */
    private String emotionType = "neutral";

    /**
     * 情感强度 (0.0-1.0)
     */
    private Float emotionIntensity = 0.5f;
}
