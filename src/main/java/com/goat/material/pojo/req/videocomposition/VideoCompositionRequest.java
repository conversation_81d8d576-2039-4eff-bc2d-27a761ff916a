package com.goat.material.pojo.req.videocomposition;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 视频合成请求
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Accessors(chain = true)
public class VideoCompositionRequest {

    /**
     * 合成任务标题
     */
    @NotBlank(message = "任务标题不能为空")
    private String title;

    /**
     * 合成任务描述
     */
    private String description;

    /**
     * 视频配置
     */
    @NotNull(message = "视频配置不能为空")
    private VideoConfig videoConfig;

    /**
     * 配音配置
     */
    private VoiceoverConfig voiceoverConfig;

    /**
     * 字幕配置
     */
    private SubtitleConfig subtitleConfig;

    /**
     * 口播配置
     */
    private TalkingHeadConfig talkingHeadConfig;

    /**
     * 输出配置
     */
    @NotNull(message = "输出配置不能为空")
    private OutputConfig outputConfig;

    /**
     * 视频素材列表
     */
    private List<VideoMaterial> videoMaterials;

    /**
     * 音频素材列表
     */
    private List<AudioMaterial> audioMaterials;

    /**
     * 图片素材列表
     */
    private List<ImageMaterial> imageMaterials;

    /**
     * 文本内容（用于生成配音和字幕）
     */
    private String textContent;

    /**
     * 背景音乐配置
     */
    private BackgroundMusicConfig backgroundMusicConfig;

    /**
     * 转场效果配置
     */
    private List<TransitionConfig> transitionConfigs;

    /**
     * 是否异步处理
     */
    private Boolean async = true;

    /**
     * 回调URL（异步处理完成后通知）
     */
    private String callbackUrl;
}
