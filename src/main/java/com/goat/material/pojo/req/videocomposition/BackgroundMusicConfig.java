package com.goat.material.pojo.req.videocomposition;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 背景音乐配置
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Accessors(chain = true)
public class BackgroundMusicConfig {

    /**
     * 是否启用背景音乐
     */
    private Boolean enabled = false;

    /**
     * 背景音乐文件路径
     */
    private String filePath;

    /**
     * 音量 (0.0-1.0)
     */
    private Float volume = 0.3f;

    /**
     * 是否循环播放
     */
    private Boolean loop = true;

    /**
     * 淡入时长（秒）
     */
    private Float fadeInDuration = 2.0f;

    /**
     * 淡出时长（秒）
     */
    private Float fadeOutDuration = 2.0f;

    /**
     * 开始时间（秒）
     */
    private Float startTime = 0.0f;

    /**
     * 结束时间（秒，null表示到视频结束）
     */
    private Float endTime;

    /**
     * 音乐类型 (ambient, upbeat, calm, dramatic)
     */
    private String musicType = "ambient";

    /**
     * 是否启用自动音量调节（避免与配音冲突）
     */
    private Boolean autoDucking = true;

    /**
     * 自动音量调节强度 (0.0-1.0)
     */
    private Float duckingStrength = 0.5f;
}
