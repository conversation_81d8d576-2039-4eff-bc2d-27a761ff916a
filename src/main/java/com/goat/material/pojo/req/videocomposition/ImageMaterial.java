package com.goat.material.pojo.req.videocomposition;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 图片素材
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Accessors(chain = true)
public class ImageMaterial {

    /**
     * 素材ID
     */
    private String id;

    /**
     * 素材文件路径或URL
     */
    @NotBlank(message = "图片素材路径不能为空")
    private String filePath;

    /**
     * 素材名称
     */
    private String name;

    /**
     * 图片在视频中的开始时间（秒）
     */
    @NotNull(message = "开始时间不能为空")
    private Float startTime;

    /**
     * 图片在视频中的结束时间（秒）
     */
    @NotNull(message = "结束时间不能为空")
    private Float endTime;

    /**
     * 图片显示时长（秒）
     */
    private Float duration;

    /**
     * 图片层级（数字越大越在上层）
     */
    private Integer layer = 1;

    /**
     * 位置X坐标
     */
    private Integer positionX = 0;

    /**
     * 位置Y坐标
     */
    private Integer positionY = 0;

    /**
     * 宽度
     */
    private Integer width;

    /**
     * 高度
     */
    private Integer height;

    /**
     * 缩放比例 (0.1-5.0)
     */
    private Float scale = 1.0f;

    /**
     * 旋转角度（度）
     */
    private Float rotation = 0.0f;

    /**
     * 透明度 (0.0-1.0)
     */
    private Float opacity = 1.0f;

    /**
     * 淡入时长（秒）
     */
    private Float fadeInDuration = 0.0f;

    /**
     * 淡出时长（秒）
     */
    private Float fadeOutDuration = 0.0f;

    /**
     * 裁剪配置
     */
    private CropConfig cropConfig;

    /**
     * 滤镜效果
     */
    private String filter;

    /**
     * 混合模式 (normal, multiply, screen, overlay)
     */
    private String blendMode = "normal";

    /**
     * 是否启用Ken Burns效果（缩放平移）
     */
    private Boolean kenBurnsEffect = false;

    /**
     * Ken Burns起始缩放
     */
    private Float kenBurnsStartScale = 1.0f;

    /**
     * Ken Burns结束缩放
     */
    private Float kenBurnsEndScale = 1.1f;

    /**
     * Ken Burns起始X位置
     */
    private Integer kenBurnsStartX = 0;

    /**
     * Ken Burns起始Y位置
     */
    private Integer kenBurnsStartY = 0;

    /**
     * Ken Burns结束X位置
     */
    private Integer kenBurnsEndX = 0;

    /**
     * Ken Burns结束Y位置
     */
    private Integer kenBurnsEndY = 0;
}
