package com.goat.material.pojo.req.videocomposition;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 输出配置
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Data
@Accessors(chain = true)
public class OutputConfig {

    /**
     * 输出文件名
     */
    @NotBlank(message = "输出文件名不能为空")
    private String fileName;

    /**
     * 输出格式 (mp4, avi, mov, mkv)
     */
    private String format = "mp4";

    /**
     * 输出质量 (low, medium, high, ultra)
     */
    private String quality = "high";

    /**
     * 是否上传到云存储
     */
    private Boolean uploadToCloud = true;

    /**
     * 云存储路径前缀
     */
    private String cloudPathPrefix = "/video-composition/";

    /**
     * 是否生成缩略图
     */
    private Boolean generateThumbnail = true;

    /**
     * 缩略图时间点（秒）
     */
    private Float thumbnailTime = 1.0f;

    /**
     * 缩略图宽度
     */
    private Integer thumbnailWidth = 320;

    /**
     * 缩略图高度
     */
    private Integer thumbnailHeight = 180;

    /**
     * 是否生成预览视频
     */
    private Boolean generatePreview = false;

    /**
     * 预览视频时长（秒）
     */
    private Float previewDuration = 10.0f;

    /**
     * 是否保留中间文件
     */
    private Boolean keepIntermediateFiles = false;

    /**
     * 输出文件大小限制（MB）
     */
    private Integer maxFileSize;

    /**
     * 是否启用快速开始（优化网络播放）
     */
    private Boolean fastStart = true;

    /**
     * 元数据标题
     */
    private String metadataTitle;

    /**
     * 元数据描述
     */
    private String metadataDescription;

    /**
     * 元数据作者
     */
    private String metadataAuthor;

    /**
     * 元数据版权
     */
    private String metadataCopyright;
}
