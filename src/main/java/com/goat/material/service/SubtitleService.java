package com.goat.material.service;

import com.goat.material.bean.mixvideo.SrtLine;
import com.goat.material.pojo.req.videocomposition.SubtitleConfig;
import com.goat.material.utils.AttacheFileUtil;
import com.goat.material.utils.HexUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字幕服务
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Slf4j
@Service
public class SubtitleService {

    /**
     * 生成字幕
     */
    public String generateSubtitle(String textContent, String voiceoverPath, SubtitleConfig config, String workDir) {
        try {
            log.info("开始生成字幕，文本长度: {}", textContent.length());

            List<SrtLine> subtitleLines;
            
            if (config.getSyncWithVoiceover() && voiceoverPath != null) {
                // 与配音同步的字幕
                subtitleLines = generateSyncedSubtitles(textContent, voiceoverPath, config);
            } else {
                // 基于时间的字幕
                subtitleLines = generateTimedSubtitles(textContent, config);
            }

            // 生成字幕文件
            String subtitlePath = createSubtitleFile(subtitleLines, config, workDir);

            log.info("字幕生成完成: {}", subtitlePath);
            return subtitlePath;

        } catch (Exception e) {
            log.error("生成字幕失败", e);
            throw new RuntimeException("生成字幕失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成与配音同步的字幕
     */
    private List<SrtLine> generateSyncedSubtitles(String textContent, String voiceoverPath, SubtitleConfig config) {
        try {
            // 获取音频总时长
            float audioDuration = AttacheFileUtil.getDurationInMill(voiceoverPath) / 1000.0f;
            
            // 分割文本为句子
            List<String> sentences = splitTextIntoSentences(textContent, config);
            
            List<SrtLine> subtitleLines = new ArrayList<>();
            float currentTime = config.getTimeOffset();
            
            for (int i = 0; i < sentences.size(); i++) {
                String sentence = sentences.get(i).trim();
                if (sentence.isEmpty()) continue;
                
                // 计算每个句子的时长（基于字符数和总时长的比例）
                float sentenceDuration = calculateSentenceDuration(sentence, sentences, audioDuration, config);
                
                // 应用字幕显示时长限制
                float displayDuration = Math.min(sentenceDuration, config.getDisplayDuration());
                
                SrtLine srtLine = new SrtLine()
                        .setContent(sentence)
                        .setStartTime((long) (currentTime * 1000))
                        .setEndTime((long) ((currentTime + displayDuration) * 1000));
                
                subtitleLines.add(srtLine);
                
                // 更新时间，考虑淡入淡出
                currentTime += sentenceDuration;
                
                // 添加句子间的间隔
                if (i < sentences.size() - 1) {
                    currentTime += 0.2f; // 200ms间隔
                }
            }
            
            return subtitleLines;
            
        } catch (Exception e) {
            throw new RuntimeException("生成同步字幕失败", e);
        }
    }

    /**
     * 生成基于时间的字幕
     */
    private List<SrtLine> generateTimedSubtitles(String textContent, SubtitleConfig config) {
        List<String> sentences = splitTextIntoSentences(textContent, config);
        List<SrtLine> subtitleLines = new ArrayList<>();
        
        float currentTime = config.getTimeOffset();
        
        for (String sentence : sentences) {
            sentence = sentence.trim();
            if (sentence.isEmpty()) continue;
            
            float duration = config.getDisplayDuration();
            
            SrtLine srtLine = new SrtLine()
                    .setContent(sentence)
                    .setStartTime((long) (currentTime * 1000))
                    .setEndTime((long) ((currentTime + duration) * 1000));
            
            subtitleLines.add(srtLine);
            currentTime += duration + 0.5f; // 500ms间隔
        }
        
        return subtitleLines;
    }

    /**
     * 分割文本为句子
     */
    private List<String> splitTextIntoSentences(String text, SubtitleConfig config) {
        List<String> sentences = new ArrayList<>();
        
        // 按句号、问号、感叹号分割
        String[] parts = text.split("[.!?]+");
        
        for (String part : parts) {
            part = part.trim();
            if (part.isEmpty()) continue;
            
            // 如果句子太长，按最大字符数分割
            if (part.length() > config.getMaxCharsPerLine()) {
                sentences.addAll(splitLongSentence(part, config));
            } else {
                sentences.add(part);
            }
        }
        
        return sentences;
    }

    /**
     * 分割长句子
     */
    private List<String> splitLongSentence(String sentence, SubtitleConfig config) {
        List<String> parts = new ArrayList<>();
        String[] words = sentence.split("\\s+");
        
        StringBuilder currentPart = new StringBuilder();
        
        for (String word : words) {
            if (currentPart.length() + word.length() + 1 > config.getMaxCharsPerLine()) {
                if (currentPart.length() > 0) {
                    parts.add(currentPart.toString().trim());
                    currentPart = new StringBuilder();
                }
            }
            
            if (currentPart.length() > 0) {
                currentPart.append(" ");
            }
            currentPart.append(word);
        }
        
        if (currentPart.length() > 0) {
            parts.add(currentPart.toString().trim());
        }
        
        return parts;
    }

    /**
     * 计算句子时长
     */
    private float calculateSentenceDuration(String sentence, List<String> allSentences, float totalDuration, SubtitleConfig config) {
        // 计算总字符数
        int totalChars = allSentences.stream().mapToInt(String::length).sum();
        
        // 基于字符数比例计算时长
        float baseDuration = (float) sentence.length() / totalChars * totalDuration;
        
        // 应用最小和最大时长限制
        float minDuration = 1.0f; // 最小1秒
        float maxDuration = config.getDisplayDuration();
        
        return Math.max(minDuration, Math.min(maxDuration, baseDuration));
    }

    /**
     * 创建字幕文件
     */
    private String createSubtitleFile(List<SrtLine> subtitleLines, SubtitleConfig config, String workDir) {
        try {
            String fileName = "subtitle_" + HexUtil.hexString(8);
            String subtitlePath;
            
            switch (config.getSubtitleType().toLowerCase()) {
                case "srt":
                    subtitlePath = workDir + "/" + fileName + ".srt";
                    createSrtFile(subtitleLines, subtitlePath);
                    break;
                case "ass":
                    subtitlePath = workDir + "/" + fileName + ".ass";
                    createAssFile(subtitleLines, config, subtitlePath);
                    break;
                case "embedded":
                default:
                    subtitlePath = workDir + "/" + fileName + ".srt";
                    createSrtFile(subtitleLines, subtitlePath);
                    break;
            }
            
            return subtitlePath;
            
        } catch (Exception e) {
            throw new RuntimeException("创建字幕文件失败", e);
        }
    }

    /**
     * 创建SRT字幕文件
     */
    private void createSrtFile(List<SrtLine> subtitleLines, String filePath) {
        try {
            List<String> lines = new ArrayList<>();
            
            for (int i = 0; i < subtitleLines.size(); i++) {
                SrtLine line = subtitleLines.get(i);
                
                // 序号
                lines.add(String.valueOf(i + 1));
                
                // 时间轴
                String startTime = formatSrtTime(line.getStartTime());
                String endTime = formatSrtTime(line.getEndTime());
                lines.add(startTime + " --> " + endTime);
                
                // 字幕内容
                lines.add(line.getContent());
                
                // 空行分隔
                lines.add("");
            }
            
            Files.write(Paths.get(filePath), lines);
            
        } catch (Exception e) {
            throw new RuntimeException("创建SRT文件失败", e);
        }
    }

    /**
     * 创建ASS字幕文件
     */
    private void createAssFile(List<SrtLine> subtitleLines, SubtitleConfig config, String filePath) {
        try {
            List<String> lines = new ArrayList<>();
            
            // ASS文件头
            lines.add("[Script Info]");
            lines.add("Title: Generated Subtitle");
            lines.add("ScriptType: v4.00+");
            lines.add("");
            
            // 样式定义
            lines.add("[V4+ Styles]");
            lines.add("Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding");
            
            String style = String.format("Style: Default,%s,%d,%s,&H00000000,%s,&H00000000,0,0,0,0,100,100,0,0,1,%d,0,%d,%d,%d,%d,1",
                    config.getFontName(),
                    config.getFontSize(),
                    convertColorToAss(config.getFontColor()),
                    convertColorToAss(config.getOutlineColor()),
                    config.getOutlineWidth(),
                    getAssAlignment(config.getAlignment()),
                    config.getMarginHorizontal(),
                    config.getMarginHorizontal(),
                    config.getMarginVertical()
            );
            lines.add(style);
            lines.add("");
            
            // 事件
            lines.add("[Events]");
            lines.add("Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text");
            
            for (SrtLine line : subtitleLines) {
                String startTime = formatAssTime(line.getStartTime());
                String endTime = formatAssTime(line.getEndTime());
                String event = String.format("Dialogue: 0,%s,%s,Default,,0,0,0,,%s",
                        startTime, endTime, line.getContent());
                lines.add(event);
            }
            
            Files.write(Paths.get(filePath), lines);
            
        } catch (Exception e) {
            throw new RuntimeException("创建ASS文件失败", e);
        }
    }

    /**
     * 格式化SRT时间
     */
    private String formatSrtTime(long milliseconds) {
        long hours = milliseconds / 3600000;
        long minutes = (milliseconds % 3600000) / 60000;
        long seconds = (milliseconds % 60000) / 1000;
        long millis = milliseconds % 1000;
        
        return String.format("%02d:%02d:%02d,%03d", hours, minutes, seconds, millis);
    }

    /**
     * 格式化ASS时间
     */
    private String formatAssTime(long milliseconds) {
        long hours = milliseconds / 3600000;
        long minutes = (milliseconds % 3600000) / 60000;
        long seconds = (milliseconds % 60000) / 1000;
        long centiseconds = (milliseconds % 1000) / 10;
        
        return String.format("%d:%02d:%02d.%02d", hours, minutes, seconds, centiseconds);
    }

    /**
     * 转换颜色格式为ASS格式
     */
    private String convertColorToAss(String hexColor) {
        if (hexColor == null || !hexColor.startsWith("#")) {
            return "&H00FFFFFF"; // 默认白色
        }
        
        String color = hexColor.substring(1);
        if (color.length() == 6) {
            // 转换RGB为BGR
            String r = color.substring(0, 2);
            String g = color.substring(2, 4);
            String b = color.substring(4, 6);
            return "&H00" + b + g + r;
        }
        
        return "&H00FFFFFF";
    }

    /**
     * 获取ASS对齐方式
     */
    private int getAssAlignment(String alignment) {
        switch (alignment.toLowerCase()) {
            case "left":
                return 1;
            case "center":
                return 2;
            case "right":
                return 3;
            default:
                return 2; // 默认居中
        }
    }
}
