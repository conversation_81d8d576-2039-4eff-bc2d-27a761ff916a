package com.goat.material.service;

import com.goat.material.bean.mixvideo.T2SFactory;
import com.goat.material.bean.schedule.mix.FfmpegCommand;
import com.goat.material.pojo.req.videocomposition.VoiceoverConfig;
import com.goat.material.utils.HexUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 配音服务
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Slf4j
@Service
public class VoiceoverService {

    private final T2SFactory t2SFactory;

    public VoiceoverService(T2SFactory t2SFactory) {
        this.t2SFactory = t2SFactory;
    }

    /**
     * 生成配音
     */
    public String generateVoiceover(String textContent, VoiceoverConfig config, String workDir) {
        try {
            log.info("开始生成配音，文本长度: {}", textContent.length());

            // 预处理文本
            String processedText = preprocessText(textContent, config);

            // 使用TTS生成音频
            String audioIdentify = t2SFactory.getT2S().text2Speech(
                    processedText,
                    config.getVoiceId(),
                    config.getSpeed(),
                    config.getVolume()
            );

            // 获取生成的音频文件
            File audioFile = t2SFactory.getT2S().getAudioFile(audioIdentify);
            if (!audioFile.exists()) {
                throw new RuntimeException("TTS生成的音频文件不存在");
            }

            // 复制到工作目录
            String outputPath = workDir + "/voiceover_" + HexUtil.hexString(8) + ".mp3";
            Files.copy(audioFile.toPath(), Paths.get(outputPath));

            // 应用音频后处理
            String processedAudioPath = postProcessAudio(outputPath, config, workDir);

            log.info("配音生成完成: {}", processedAudioPath);
            return processedAudioPath;

        } catch (Exception e) {
            log.error("生成配音失败", e);
            throw new RuntimeException("生成配音失败: " + e.getMessage(), e);
        }
    }

    /**
     * 预处理文本
     */
    private String preprocessText(String text, VoiceoverConfig config) {
        if (!StringUtils.hasText(text)) {
            throw new IllegalArgumentException("文本内容不能为空");
        }

        String processedText = text;

        // 清理文本
        processedText = processedText.trim();
        processedText = processedText.replaceAll("\\s+", " "); // 多个空格替换为单个空格

        // 添加停顿
        if (config.getAddPauses()) {
            // 在句号、问号、感叹号后添加停顿标记
            processedText = processedText.replaceAll("([.!?])\\s*", "$1[PAUSE:" + config.getSentencePause() + "]");
            
            // 在段落分隔符后添加更长的停顿
            processedText = processedText.replaceAll("\\n\\s*\\n", "[PAUSE:" + config.getParagraphPause() + "]");
        }

        // 根据TTS引擎调整文本格式
        if ("elevenlabs".equals(config.getTtsEngine())) {
            processedText = adjustTextForElevenLabs(processedText, config);
        } else if ("ali".equals(config.getTtsEngine())) {
            processedText = adjustTextForAli(processedText, config);
        }

        log.debug("文本预处理完成，原始长度: {}, 处理后长度: {}", text.length(), processedText.length());
        return processedText;
    }

    /**
     * 为ElevenLabs调整文本
     */
    private String adjustTextForElevenLabs(String text, VoiceoverConfig config) {
        // ElevenLabs支持SSML标记
        if (config.getEmotionEnabled()) {
            text = String.format("<speak><prosody emotion=\"%s\" intensity=\"%.1f\">%s</prosody></speak>",
                    config.getEmotionType(), config.getEmotionIntensity(), text);
        }
        
        if (config.getPitch() != null && !config.getPitch().equals(1.0f)) {
            text = String.format("<speak><prosody pitch=\"%+.0f%%\">%s</prosody></speak>",
                    (config.getPitch() - 1.0f) * 100, text);
        }
        
        return text;
    }

    /**
     * 为阿里云TTS调整文本
     */
    private String adjustTextForAli(String text, VoiceoverConfig config) {
        // 阿里云TTS的特殊处理
        // 可以添加SSML标记或其他格式调整
        return text;
    }

    /**
     * 音频后处理
     */
    private String postProcessAudio(String inputPath, VoiceoverConfig config, String workDir) {
        try {
            String currentPath = inputPath;
            
            // 调整音调
            if (config.getPitch() != null && !config.getPitch().equals(1.0f)) {
                currentPath = adjustPitch(currentPath, config.getPitch(), workDir);
            }

            // 调整采样率
            if (config.getSampleRate() != null && !config.getSampleRate().equals(44100)) {
                currentPath = adjustSampleRate(currentPath, config.getSampleRate(), workDir);
            }

            // 调整音频码率
            if (config.getAudioBitrate() != null && !config.getAudioBitrate().equals(128)) {
                currentPath = adjustBitrate(currentPath, config.getAudioBitrate(), workDir);
            }

            // 添加开始时间偏移
            if (config.getStartOffset() != null && config.getStartOffset() > 0) {
                currentPath = addStartOffset(currentPath, config.getStartOffset(), workDir);
            }

            // 音频格式转换
            if (!"mp3".equals(config.getAudioFormat())) {
                currentPath = convertAudioFormat(currentPath, config.getAudioFormat(), workDir);
            }

            return currentPath;

        } catch (Exception e) {
            log.error("音频后处理失败", e);
            throw new RuntimeException("音频后处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调整音调
     */
    private String adjustPitch(String inputPath, Float pitch, String workDir) {
        try {
            String outputPath = workDir + "/pitch_adjusted_" + HexUtil.hexString(8) + ".mp3";
            
            // 使用FFmpeg的asetrate和atempo滤镜调整音调
            // pitch > 1.0 提高音调，pitch < 1.0 降低音调
            float rate = pitch;
            float tempo = 1.0f / pitch;
            
            List<String> command = Arrays.asList(
                    "-i", inputPath,
                    "-af", String.format("asetrate=44100*%.2f,atempo=%.2f", rate, tempo),
                    "-c:a", "libmp3lame",
                    "-q:a", "2",
                    outputPath
            );
            
            FfmpegCommand.runCommand(command);
            
            // 删除输入文件
            Files.deleteIfExists(Paths.get(inputPath));
            
            return outputPath;
        } catch (Exception e) {
            throw new RuntimeException("调整音调失败", e);
        }
    }

    /**
     * 调整采样率
     */
    private String adjustSampleRate(String inputPath, Integer sampleRate, String workDir) {
        try {
            String outputPath = workDir + "/samplerate_adjusted_" + HexUtil.hexString(8) + ".mp3";
            
            List<String> command = Arrays.asList(
                    "-i", inputPath,
                    "-ar", sampleRate.toString(),
                    "-c:a", "libmp3lame",
                    "-q:a", "2",
                    outputPath
            );
            
            FfmpegCommand.runCommand(command);
            
            // 删除输入文件
            Files.deleteIfExists(Paths.get(inputPath));
            
            return outputPath;
        } catch (Exception e) {
            throw new RuntimeException("调整采样率失败", e);
        }
    }

    /**
     * 调整音频码率
     */
    private String adjustBitrate(String inputPath, Integer bitrate, String workDir) {
        try {
            String outputPath = workDir + "/bitrate_adjusted_" + HexUtil.hexString(8) + ".mp3";
            
            List<String> command = Arrays.asList(
                    "-i", inputPath,
                    "-b:a", bitrate + "k",
                    "-c:a", "libmp3lame",
                    outputPath
            );
            
            FfmpegCommand.runCommand(command);
            
            // 删除输入文件
            Files.deleteIfExists(Paths.get(inputPath));
            
            return outputPath;
        } catch (Exception e) {
            throw new RuntimeException("调整音频码率失败", e);
        }
    }

    /**
     * 添加开始时间偏移
     */
    private String addStartOffset(String inputPath, Float startOffset, String workDir) {
        try {
            String outputPath = workDir + "/offset_adjusted_" + HexUtil.hexString(8) + ".mp3";
            
            // 生成静音音频
            String silencePath = workDir + "/silence_" + HexUtil.hexString(8) + ".mp3";
            List<String> silenceCommand = Arrays.asList(
                    "-f", "lavfi",
                    "-i", "anullsrc=channel_layout=stereo:sample_rate=44100:duration=" + startOffset,
                    "-c:a", "libmp3lame",
                    "-q:a", "2",
                    silencePath
            );
            FfmpegCommand.runCommand(silenceCommand);
            
            // 合并静音和原音频
            List<File> audioFiles = Arrays.asList(new File(silencePath), new File(inputPath));
            FfmpegCommand.mergeAudio(workDir, outputPath, audioFiles, true);
            
            // 删除输入文件
            Files.deleteIfExists(Paths.get(inputPath));
            
            return outputPath;
        } catch (Exception e) {
            throw new RuntimeException("添加开始偏移失败", e);
        }
    }

    /**
     * 转换音频格式
     */
    private String convertAudioFormat(String inputPath, String format, String workDir) {
        try {
            String outputPath = workDir + "/converted_" + HexUtil.hexString(8) + "." + format;
            
            List<String> command = new ArrayList<>();
            command.addAll(Arrays.asList("-i", inputPath));
            
            // 根据格式设置编码器
            switch (format.toLowerCase()) {
                case "wav":
                    command.addAll(Arrays.asList("-c:a", "pcm_s16le"));
                    break;
                case "aac":
                    command.addAll(Arrays.asList("-c:a", "aac", "-b:a", "128k"));
                    break;
                case "ogg":
                    command.addAll(Arrays.asList("-c:a", "libvorbis", "-q:a", "5"));
                    break;
                case "flac":
                    command.addAll(Arrays.asList("-c:a", "flac"));
                    break;
                default:
                    command.addAll(Arrays.asList("-c:a", "libmp3lame", "-q:a", "2"));
                    break;
            }
            
            command.add(outputPath);
            FfmpegCommand.runCommand(command);
            
            // 删除输入文件
            Files.deleteIfExists(Paths.get(inputPath));
            
            return outputPath;
        } catch (Exception e) {
            throw new RuntimeException("转换音频格式失败", e);
        }
    }
}
