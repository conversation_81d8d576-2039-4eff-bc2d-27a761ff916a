package com.goat.material.service;

import com.goat.material.bean.schedule.mix.FfmpegCommand;
import com.goat.material.pojo.req.videocomposition.*;
import com.goat.material.utils.HexUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 视频合并服务
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Slf4j
@Service
public class VideoMergeService {

    /**
     * 合并视频
     */
    public String mergeVideo(VideoCompositionRequest request, String voiceoverPath, String subtitlePath, String workDir) {
        try {
            log.info("开始合成视频");

            // 构建FFmpeg命令
            List<String> command = buildFfmpegCommand(request, voiceoverPath, subtitlePath, workDir);
            
            // 执行FFmpeg命令
            FfmpegCommand.runCommand(command);
            
            String outputPath = workDir + "/output_" + HexUtil.hexString(8) + "." + request.getOutputConfig().getFormat();
            
            log.info("视频合成完成: {}", outputPath);
            return outputPath;

        } catch (Exception e) {
            log.error("视频合成失败", e);
            throw new RuntimeException("视频合成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建FFmpeg命令
     */
    private List<String> buildFfmpegCommand(VideoCompositionRequest request, String voiceoverPath, String subtitlePath, String workDir) {
        List<String> command = new ArrayList<>();
        List<String> inputs = new ArrayList<>();
        List<String> filterComplex = new ArrayList<>();
        List<String> maps = new ArrayList<>();
        
        int inputIndex = 0;
        
        // 添加视频输入
        if (request.getVideoMaterials() != null && !request.getVideoMaterials().isEmpty()) {
            for (VideoMaterial video : request.getVideoMaterials()) {
                inputs.addAll(Arrays.asList("-i", video.getFilePath()));
                inputIndex++;
            }
        }
        
        // 添加图片输入
        if (request.getImageMaterials() != null && !request.getImageMaterials().isEmpty()) {
            for (ImageMaterial image : request.getImageMaterials()) {
                inputs.addAll(Arrays.asList("-i", image.getFilePath()));
                inputIndex++;
            }
        }
        
        // 添加音频输入
        if (request.getAudioMaterials() != null && !request.getAudioMaterials().isEmpty()) {
            for (AudioMaterial audio : request.getAudioMaterials()) {
                inputs.addAll(Arrays.asList("-i", audio.getFilePath()));
                inputIndex++;
            }
        }
        
        // 添加配音输入
        if (voiceoverPath != null) {
            inputs.addAll(Arrays.asList("-i", voiceoverPath));
            inputIndex++;
        }
        
        // 添加背景音乐输入
        if (request.getBackgroundMusicConfig() != null && request.getBackgroundMusicConfig().getEnabled()) {
            inputs.addAll(Arrays.asList("-i", request.getBackgroundMusicConfig().getFilePath()));
            inputIndex++;
        }
        
        // 构建视频滤镜
        String videoFilter = buildVideoFilter(request, workDir);
        if (StringUtils.hasText(videoFilter)) {
            filterComplex.add(videoFilter);
        }
        
        // 构建音频滤镜
        String audioFilter = buildAudioFilter(request, voiceoverPath);
        if (StringUtils.hasText(audioFilter)) {
            filterComplex.add(audioFilter);
        }
        
        // 组装完整命令
        command.addAll(inputs);
        
        if (!filterComplex.isEmpty()) {
            command.addAll(Arrays.asList("-filter_complex", String.join(";", filterComplex)));
        }
        
        // 添加字幕
        if (subtitlePath != null && request.getSubtitleConfig().getEnabled()) {
            if ("embedded".equals(request.getSubtitleConfig().getSubtitleType())) {
                command.addAll(buildSubtitleFilter(subtitlePath, request.getSubtitleConfig()));
            }
        }
        
        // 输出设置
        command.addAll(buildOutputSettings(request, workDir));
        
        return command;
    }

    /**
     * 构建视频滤镜
     */
    private String buildVideoFilter(VideoCompositionRequest request, String workDir) {
        List<String> filters = new ArrayList<>();
        
        VideoConfig videoConfig = request.getVideoConfig();
        
        // 创建背景
        String background = String.format("color=c=%s:size=%dx%d:duration=%f:rate=%d[bg]",
                videoConfig.getBackgroundColor(),
                videoConfig.getWidth(),
                videoConfig.getHeight(),
                videoConfig.getDuration(),
                videoConfig.getFrameRate());
        filters.add(background);
        
        // 处理视频素材
        if (request.getVideoMaterials() != null) {
            for (int i = 0; i < request.getVideoMaterials().size(); i++) {
                VideoMaterial video = request.getVideoMaterials().get(i);
                String videoFilter = buildSingleVideoFilter(i, video, videoConfig);
                if (StringUtils.hasText(videoFilter)) {
                    filters.add(videoFilter);
                }
            }
        }
        
        // 处理图片素材
        if (request.getImageMaterials() != null) {
            int videoCount = request.getVideoMaterials() != null ? request.getVideoMaterials().size() : 0;
            for (int i = 0; i < request.getImageMaterials().size(); i++) {
                ImageMaterial image = request.getImageMaterials().get(i);
                String imageFilter = buildSingleImageFilter(videoCount + i, image, videoConfig);
                if (StringUtils.hasText(imageFilter)) {
                    filters.add(imageFilter);
                }
            }
        }
        
        // 叠加所有层
        String overlayFilter = buildOverlayFilter(request);
        if (StringUtils.hasText(overlayFilter)) {
            filters.add(overlayFilter);
        }
        
        return String.join(";", filters);
    }

    /**
     * 构建单个视频滤镜
     */
    private String buildSingleVideoFilter(int index, VideoMaterial video, VideoConfig videoConfig) {
        List<String> filters = new ArrayList<>();
        
        String input = "[" + index + ":v]";
        String output = "[v" + index + "]";
        
        // 裁剪
        if (video.getCropConfig() != null && video.getCropConfig().getEnabled()) {
            CropConfig crop = video.getCropConfig();
            filters.add(String.format("crop=%d:%d:%d:%d", crop.getWidth(), crop.getHeight(), crop.getX(), crop.getY()));
        }
        
        // 缩放
        if (video.getWidth() != null && video.getHeight() != null) {
            filters.add(String.format("scale=%d:%d", video.getWidth(), video.getHeight()));
        } else if (video.getScale() != null && !video.getScale().equals(1.0f)) {
            filters.add(String.format("scale=iw*%.2f:ih*%.2f", video.getScale(), video.getScale()));
        }
        
        // 旋转
        if (video.getRotation() != null && !video.getRotation().equals(0.0f)) {
            filters.add(String.format("rotate=%.2f*PI/180", video.getRotation()));
        }
        
        // 透明度
        if (video.getOpacity() != null && !video.getOpacity().equals(1.0f)) {
            filters.add(String.format("format=yuva420p,colorchannelmixer=aa=%.2f", video.getOpacity()));
        }
        
        // 播放速度
        if (video.getSpeed() != null && !video.getSpeed().equals(1.0f)) {
            filters.add(String.format("setpts=%.2f*PTS", 1.0f / video.getSpeed()));
        }
        
        // 滤镜效果
        if (StringUtils.hasText(video.getFilter())) {
            filters.add(video.getFilter());
        }
        
        if (filters.isEmpty()) {
            return "";
        }
        
        return input + String.join(",", filters) + output;
    }

    /**
     * 构建单个图片滤镜
     */
    private String buildSingleImageFilter(int index, ImageMaterial image, VideoConfig videoConfig) {
        List<String> filters = new ArrayList<>();
        
        String input = "[" + index + ":v]";
        String output = "[i" + index + "]";
        
        // 图片循环显示
        float duration = image.getEndTime() - image.getStartTime();
        filters.add(String.format("loop=loop=-1:size=1:start=0"));
        filters.add(String.format("setpts=PTS-STARTPTS"));
        
        // 缩放
        if (image.getWidth() != null && image.getHeight() != null) {
            filters.add(String.format("scale=%d:%d", image.getWidth(), image.getHeight()));
        } else if (image.getScale() != null && !image.getScale().equals(1.0f)) {
            filters.add(String.format("scale=iw*%.2f:ih*%.2f", image.getScale(), image.getScale()));
        }
        
        // Ken Burns效果
        if (image.getKenBurnsEffect() != null && image.getKenBurnsEffect()) {
            String kenBurns = String.format("zoompan=z='min(zoom+0.0015,%.2f)':d=%d:x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)'",
                    image.getKenBurnsEndScale(), (int) (duration * videoConfig.getFrameRate()));
            filters.add(kenBurns);
        }
        
        // 其他效果（旋转、透明度等）
        if (image.getRotation() != null && !image.getRotation().equals(0.0f)) {
            filters.add(String.format("rotate=%.2f*PI/180", image.getRotation()));
        }
        
        if (image.getOpacity() != null && !image.getOpacity().equals(1.0f)) {
            filters.add(String.format("format=yuva420p,colorchannelmixer=aa=%.2f", image.getOpacity()));
        }
        
        if (filters.isEmpty()) {
            return "";
        }
        
        return input + String.join(",", filters) + output;
    }

    /**
     * 构建叠加滤镜
     */
    private String buildOverlayFilter(VideoCompositionRequest request) {
        List<String> overlays = new ArrayList<>();
        String currentOutput = "[bg]";
        
        // 按层级排序所有素材
        List<MaterialWithLayer> materials = new ArrayList<>();
        
        if (request.getVideoMaterials() != null) {
            for (int i = 0; i < request.getVideoMaterials().size(); i++) {
                VideoMaterial video = request.getVideoMaterials().get(i);
                materials.add(new MaterialWithLayer("v" + i, video.getLayer(), video.getPositionX(), video.getPositionY()));
            }
        }
        
        if (request.getImageMaterials() != null) {
            int videoCount = request.getVideoMaterials() != null ? request.getVideoMaterials().size() : 0;
            for (int i = 0; i < request.getImageMaterials().size(); i++) {
                ImageMaterial image = request.getImageMaterials().get(i);
                materials.add(new MaterialWithLayer("i" + i, image.getLayer(), image.getPositionX(), image.getPositionY()));
            }
        }
        
        // 按层级排序
        materials.sort((a, b) -> Integer.compare(a.layer, b.layer));
        
        // 构建叠加链
        for (int i = 0; i < materials.size(); i++) {
            MaterialWithLayer material = materials.get(i);
            String nextOutput = i == materials.size() - 1 ? "[vout]" : "[overlay" + i + "]";
            
            String overlay = String.format("%s[%s]overlay=%d:%d%s",
                    currentOutput,
                    material.name,
                    material.x,
                    material.y,
                    nextOutput);
            
            overlays.add(overlay);
            currentOutput = nextOutput;
        }
        
        return String.join(";", overlays);
    }

    /**
     * 构建音频滤镜
     */
    private String buildAudioFilter(VideoCompositionRequest request, String voiceoverPath) {
        List<String> audioInputs = new ArrayList<>();
        List<String> audioFilters = new ArrayList<>();
        
        int audioIndex = 0;
        
        // 收集所有音频输入
        if (request.getAudioMaterials() != null) {
            for (AudioMaterial audio : request.getAudioMaterials()) {
                audioInputs.add("[" + audioIndex + ":a]");
                audioIndex++;
            }
        }
        
        if (voiceoverPath != null) {
            audioInputs.add("[" + audioIndex + ":a]");
            audioIndex++;
        }
        
        if (request.getBackgroundMusicConfig() != null && request.getBackgroundMusicConfig().getEnabled()) {
            audioInputs.add("[" + audioIndex + ":a]");
        }
        
        if (audioInputs.size() > 1) {
            // 混合多个音频
            String mixFilter = String.join("", audioInputs) + 
                    String.format("amix=inputs=%d:duration=first:normalize=0[aout]", audioInputs.size());
            audioFilters.add(mixFilter);
        } else if (audioInputs.size() == 1) {
            audioFilters.add(audioInputs.get(0) + "copy[aout]");
        }
        
        return String.join(";", audioFilters);
    }

    /**
     * 构建字幕滤镜
     */
    private List<String> buildSubtitleFilter(String subtitlePath, SubtitleConfig config) {
        List<String> command = new ArrayList<>();
        
        if ("embedded".equals(config.getSubtitleType())) {
            // 嵌入字幕
            String subtitleFilter = String.format("subtitles=%s:force_style='FontName=%s,FontSize=%d,PrimaryColour=%s,OutlineColour=%s,Outline=%d,Alignment=%d'",
                    subtitlePath,
                    config.getFontName(),
                    config.getFontSize(),
                    convertColorToAss(config.getFontColor()),
                    convertColorToAss(config.getOutlineColor()),
                    config.getOutlineWidth(),
                    getAssAlignment(config.getAlignment()));
            
            command.addAll(Arrays.asList("-vf", subtitleFilter));
        }
        
        return command;
    }

    /**
     * 构建输出设置
     */
    private List<String> buildOutputSettings(VideoCompositionRequest request, String workDir) {
        List<String> settings = new ArrayList<>();
        
        VideoConfig videoConfig = request.getVideoConfig();
        OutputConfig outputConfig = request.getOutputConfig();
        
        // 视频编码设置
        settings.addAll(Arrays.asList("-c:v", videoConfig.getCodec()));
        settings.addAll(Arrays.asList("-preset", videoConfig.getPreset()));
        settings.addAll(Arrays.asList("-crf", videoConfig.getQuality().toString()));
        settings.addAll(Arrays.asList("-r", videoConfig.getFrameRate().toString()));
        
        // 音频编码设置
        settings.addAll(Arrays.asList("-c:a", "aac"));
        settings.addAll(Arrays.asList("-b:a", "128k"));
        
        // 输出格式
        settings.addAll(Arrays.asList("-f", outputConfig.getFormat()));
        
        // 快速开始
        if (outputConfig.getFastStart()) {
            settings.addAll(Arrays.asList("-movflags", "+faststart"));
        }
        
        // 输出文件
        String outputPath = workDir + "/output_" + HexUtil.hexString(8) + "." + outputConfig.getFormat();
        settings.add(outputPath);
        
        return settings;
    }

    /**
     * 生成缩略图
     */
    public String generateThumbnail(String videoPath, OutputConfig config, String workDir) {
        try {
            String thumbnailPath = workDir + "/thumbnail_" + HexUtil.hexString(8) + ".jpg";
            
            List<String> command = Arrays.asList(
                    "-i", videoPath,
                    "-ss", config.getThumbnailTime().toString(),
                    "-vframes", "1",
                    "-s", config.getThumbnailWidth() + "x" + config.getThumbnailHeight(),
                    "-q:v", "2",
                    thumbnailPath
            );
            
            FfmpegCommand.runCommand(command);
            return thumbnailPath;
            
        } catch (Exception e) {
            log.error("生成缩略图失败", e);
            return null;
        }
    }

    /**
     * 生成预览视频
     */
    public String generatePreview(String videoPath, OutputConfig config, String workDir) {
        try {
            String previewPath = workDir + "/preview_" + HexUtil.hexString(8) + "." + config.getFormat();
            
            List<String> command = Arrays.asList(
                    "-i", videoPath,
                    "-t", config.getPreviewDuration().toString(),
                    "-c:v", "libx264",
                    "-preset", "fast",
                    "-crf", "28",
                    "-c:a", "aac",
                    "-b:a", "64k",
                    previewPath
            );
            
            FfmpegCommand.runCommand(command);
            return previewPath;
            
        } catch (Exception e) {
            log.error("生成预览视频失败", e);
            return null;
        }
    }

    // 辅助类
    private static class MaterialWithLayer {
        String name;
        Integer layer;
        Integer x;
        Integer y;
        
        MaterialWithLayer(String name, Integer layer, Integer x, Integer y) {
            this.name = name;
            this.layer = layer != null ? layer : 1;
            this.x = x != null ? x : 0;
            this.y = y != null ? y : 0;
        }
    }

    // 辅助方法
    private String convertColorToAss(String hexColor) {
        // 实现颜色转换逻辑
        return hexColor;
    }

    private int getAssAlignment(String alignment) {
        switch (alignment.toLowerCase()) {
            case "left": return 1;
            case "center": return 2;
            case "right": return 3;
            default: return 2;
        }
    }
}
