package com.goat.material.service;

import com.goat.material.bean.qcos.QcosBean;
import com.goat.material.pojo.req.videocomposition.VideoCompositionRequest;
import com.goat.material.pojo.vo.videocomposition.VideoCompositionResponse;
import com.goat.material.utils.HexUtil;
import com.goat.material.utils.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 视频合成服务
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Slf4j
@Service
public class VideoCompositionService {

    @Value("${video.composition.tmp.dir:/data/tmp/video-composition}")
    private String videoCompositionTmpDir;

    @Value("${video.composition.output.dir:/data/output/video-composition}")
    private String videoCompositionOutputDir;

    private final VoiceoverService voiceoverService;
    private final SubtitleService subtitleService;
    private final VideoMergeService videoMergeService;
    private final QcosBean qcosBean;

    // 存储任务状态
    private final ConcurrentHashMap<String, VideoCompositionResponse> taskStatusMap = new ConcurrentHashMap<>();

    public VideoCompositionService(VoiceoverService voiceoverService, 
                                 SubtitleService subtitleService,
                                 VideoMergeService videoMergeService,
                                 QcosBean qcosBean) {
        this.voiceoverService = voiceoverService;
        this.subtitleService = subtitleService;
        this.videoMergeService = videoMergeService;
        this.qcosBean = qcosBean;
    }

    /**
     * 创建视频合成任务
     */
    public VideoCompositionResponse createCompositionTask(VideoCompositionRequest request) {
        String taskId = generateTaskId();
        
        VideoCompositionResponse response = new VideoCompositionResponse()
                .setTaskId(taskId)
                .setStatus("pending")
                .setTitle(request.getTitle())
                .setDescription(request.getDescription())
                .setProgress(0)
                .setCreatedAt(new Date())
                .setCreatorId(SpringUtil.getUserInfo().getId())
                .setCreatorName(SpringUtil.getUserInfo().getUsername());

        taskStatusMap.put(taskId, response);

        if (request.getAsync()) {
            // 异步处理
            CompletableFuture.runAsync(() -> processComposition(taskId, request))
                    .exceptionally(ex -> {
                        log.error("视频合成任务失败: {}", taskId, ex);
                        updateTaskStatus(taskId, "failed", ex.getMessage());
                        return null;
                    });
        } else {
            // 同步处理
            processComposition(taskId, request);
        }

        return response;
    }

    /**
     * 获取任务状态
     */
    public VideoCompositionResponse getTaskStatus(String taskId) {
        return taskStatusMap.get(taskId);
    }

    /**
     * 处理视频合成
     */
    private void processComposition(String taskId, VideoCompositionRequest request) {
        try {
            updateTaskStatus(taskId, "processing", null);
            updateTaskProgress(taskId, 5);

            // 创建工作目录
            String workDir = createWorkDirectory(taskId);
            
            // 第一步：生成配音
            String voiceoverPath = null;
            if (request.getVoiceoverConfig() != null && request.getVoiceoverConfig().getEnabled()) {
                log.info("开始生成配音，任务ID: {}", taskId);
                voiceoverPath = voiceoverService.generateVoiceover(
                        request.getTextContent(), 
                        request.getVoiceoverConfig(), 
                        workDir
                );
                updateTaskProgress(taskId, 25);
            }

            // 第二步：生成字幕
            String subtitlePath = null;
            if (request.getSubtitleConfig() != null && request.getSubtitleConfig().getEnabled()) {
                log.info("开始生成字幕，任务ID: {}", taskId);
                subtitlePath = subtitleService.generateSubtitle(
                        request.getTextContent(),
                        voiceoverPath,
                        request.getSubtitleConfig(),
                        workDir
                );
                updateTaskProgress(taskId, 45);
            }

            // 第三步：合成视频
            log.info("开始合成视频，任务ID: {}", taskId);
            String outputVideoPath = videoMergeService.mergeVideo(
                    request,
                    voiceoverPath,
                    subtitlePath,
                    workDir
            );
            updateTaskProgress(taskId, 80);

            // 第四步：生成缩略图和预览
            String thumbnailPath = null;
            String previewVideoPath = null;
            if (request.getOutputConfig().getGenerateThumbnail()) {
                thumbnailPath = videoMergeService.generateThumbnail(
                        outputVideoPath, 
                        request.getOutputConfig(),
                        workDir
                );
            }
            
            if (request.getOutputConfig().getGeneratePreview()) {
                previewVideoPath = videoMergeService.generatePreview(
                        outputVideoPath,
                        request.getOutputConfig(),
                        workDir
                );
            }
            updateTaskProgress(taskId, 90);

            // 第五步：上传到云存储
            String outputVideoUrl = null;
            String thumbnailUrl = null;
            String previewVideoUrl = null;
            String subtitleUrl = null;
            String voiceoverUrl = null;

            if (request.getOutputConfig().getUploadToCloud()) {
                log.info("开始上传文件到云存储，任务ID: {}", taskId);
                
                // 上传主视频
                outputVideoUrl = uploadToCloud(outputVideoPath, request.getOutputConfig().getCloudPathPrefix());
                
                // 上传缩略图
                if (thumbnailPath != null) {
                    thumbnailUrl = uploadToCloud(thumbnailPath, request.getOutputConfig().getCloudPathPrefix());
                }
                
                // 上传预览视频
                if (previewVideoPath != null) {
                    previewVideoUrl = uploadToCloud(previewVideoPath, request.getOutputConfig().getCloudPathPrefix());
                }
                
                // 上传字幕文件
                if (subtitlePath != null) {
                    subtitleUrl = uploadToCloud(subtitlePath, request.getOutputConfig().getCloudPathPrefix());
                }
                
                // 上传配音文件
                if (voiceoverPath != null) {
                    voiceoverUrl = uploadToCloud(voiceoverPath, request.getOutputConfig().getCloudPathPrefix());
                }
            }

            // 更新任务完成状态
            VideoCompositionResponse response = taskStatusMap.get(taskId);
            response.setStatus("completed")
                    .setProgress(100)
                    .setCompletedAt(new Date())
                    .setOutputVideoPath(outputVideoPath)
                    .setOutputVideoUrl(outputVideoUrl)
                    .setThumbnailPath(thumbnailPath)
                    .setThumbnailUrl(thumbnailUrl)
                    .setPreviewVideoPath(previewVideoPath)
                    .setPreviewVideoUrl(previewVideoUrl)
                    .setSubtitlePath(subtitlePath)
                    .setSubtitleUrl(subtitleUrl)
                    .setVoiceoverPath(voiceoverPath)
                    .setVoiceoverUrl(voiceoverUrl);

            // 获取视频信息
            updateVideoInfo(response, outputVideoPath);

            log.info("视频合成任务完成，任务ID: {}", taskId);

            // 清理临时文件
            if (!request.getOutputConfig().getKeepIntermediateFiles()) {
                cleanupWorkDirectory(workDir);
            }

        } catch (Exception e) {
            log.error("视频合成任务失败，任务ID: {}", taskId, e);
            updateTaskStatus(taskId, "failed", e.getMessage());
        }
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        return "vc_" + HexUtil.hexString(16) + "_" + System.currentTimeMillis();
    }

    /**
     * 创建工作目录
     */
    private String createWorkDirectory(String taskId) {
        String workDir = videoCompositionTmpDir + "/" + taskId;
        File dir = new File(workDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return workDir;
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(String taskId, String status, String errorMessage) {
        VideoCompositionResponse response = taskStatusMap.get(taskId);
        if (response != null) {
            response.setStatus(status);
            if (errorMessage != null) {
                response.setErrorMessage(errorMessage);
            }
            if ("processing".equals(status)) {
                response.setStartedAt(new Date());
            }
        }
    }

    /**
     * 更新任务进度
     */
    private void updateTaskProgress(String taskId, Integer progress) {
        VideoCompositionResponse response = taskStatusMap.get(taskId);
        if (response != null) {
            response.setProgress(progress);
        }
    }

    /**
     * 上传文件到云存储
     */
    private String uploadToCloud(String filePath, String cloudPathPrefix) {
        if (filePath == null) return null;
        
        File file = new File(filePath);
        if (!file.exists()) return null;
        
        String cloudPath = cloudPathPrefix + file.getName();
        return qcosBean.insert(cloudPath, filePath, false);
    }

    /**
     * 更新视频信息
     */
    private void updateVideoInfo(VideoCompositionResponse response, String videoPath) {
        // TODO: 使用FFmpeg获取视频信息
        // 这里可以调用FFmpegFrameGrabber获取视频的宽高、时长、文件大小等信息
    }

    /**
     * 清理工作目录
     */
    private void cleanupWorkDirectory(String workDir) {
        try {
            File dir = new File(workDir);
            if (dir.exists()) {
                deleteDirectory(dir);
            }
        } catch (Exception e) {
            log.warn("清理工作目录失败: {}", workDir, e);
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(File directory) {
        if (directory.isDirectory()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteDirectory(file);
                }
            }
        }
        directory.delete();
    }
}
