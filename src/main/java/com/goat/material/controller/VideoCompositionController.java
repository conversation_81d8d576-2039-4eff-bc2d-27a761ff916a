package com.goat.material.controller;

import com.goat.material.pojo.req.videocomposition.VideoCompositionRequest;
import com.goat.material.pojo.vo.videocomposition.VideoCompositionResponse;
import com.goat.material.service.VideoCompositionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 视频合成控制器
 *
 * <AUTHOR>
 * @date 2025/1/20
 */
@Slf4j
@RestController
@RequestMapping("/v2/video-composition")
public class VideoCompositionController {

    private final VideoCompositionService videoCompositionService;

    public VideoCompositionController(VideoCompositionService videoCompositionService) {
        this.videoCompositionService = videoCompositionService;
    }

    /**
     * 创建视频合成任务
     */
    @PostMapping("/create")
    public ResponseEntity<VideoCompositionResponse> createComposition(@Valid @RequestBody VideoCompositionRequest request) {
        try {
            log.info("收到视频合成请求: {}", request.getTitle());
            VideoCompositionResponse response = videoCompositionService.createCompositionTask(request);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("创建视频合成任务失败", e);
            return ResponseEntity.badRequest().body(
                    new VideoCompositionResponse()
                            .setStatus("failed")
                            .setErrorMessage(e.getMessage())
            );
        }
    }

    /**
     * 获取任务状态
     */
    @GetMapping("/status/{taskId}")
    public ResponseEntity<VideoCompositionResponse> getTaskStatus(@PathVariable String taskId) {
        try {
            VideoCompositionResponse response = videoCompositionService.getTaskStatus(taskId);
            if (response == null) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取任务状态失败: {}", taskId, e);
            return ResponseEntity.badRequest().body(
                    new VideoCompositionResponse()
                            .setTaskId(taskId)
                            .setStatus("failed")
                            .setErrorMessage(e.getMessage())
            );
        }
    }

    /**
     * 取消任务
     */
    @PostMapping("/cancel/{taskId}")
    public ResponseEntity<String> cancelTask(@PathVariable String taskId) {
        try {
            // TODO: 实现任务取消逻辑
            log.info("取消任务: {}", taskId);
            return ResponseEntity.ok("任务取消成功");
        } catch (Exception e) {
            log.error("取消任务失败: {}", taskId, e);
            return ResponseEntity.badRequest().body("取消任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取支持的配音演员列表
     */
    @GetMapping("/voices")
    public ResponseEntity<?> getVoices() {
        try {
            // 这里可以调用MixVideoService的getLanguages方法
            return ResponseEntity.ok("获取配音演员列表");
        } catch (Exception e) {
            log.error("获取配音演员列表失败", e);
            return ResponseEntity.badRequest().body("获取配音演员列表失败: " + e.getMessage());
        }
    }

    /**
     * 预览配音
     */
    @PostMapping("/preview/voiceover")
    public ResponseEntity<String> previewVoiceover(@RequestBody PreviewVoiceoverRequest request) {
        try {
            log.info("预览配音请求: {}", request.getText());
            // TODO: 实现配音预览逻辑
            return ResponseEntity.ok("配音预览生成成功");
        } catch (Exception e) {
            log.error("预览配音失败", e);
            return ResponseEntity.badRequest().body("预览配音失败: " + e.getMessage());
        }
    }

    /**
     * 预览字幕
     */
    @PostMapping("/preview/subtitle")
    public ResponseEntity<String> previewSubtitle(@RequestBody PreviewSubtitleRequest request) {
        try {
            log.info("预览字幕请求: {}", request.getText());
            // TODO: 实现字幕预览逻辑
            return ResponseEntity.ok("字幕预览生成成功");
        } catch (Exception e) {
            log.error("预览字幕失败", e);
            return ResponseEntity.badRequest().body("预览字幕失败: " + e.getMessage());
        }
    }

    /**
     * 获取模板列表
     */
    @GetMapping("/templates")
    public ResponseEntity<?> getTemplates(@RequestParam(defaultValue = "1") int page,
                                         @RequestParam(defaultValue = "20") int pageSize,
                                         @RequestParam(required = false) String category) {
        try {
            log.info("获取模板列表，页码: {}, 页大小: {}, 分类: {}", page, pageSize, category);
            // TODO: 实现模板列表获取逻辑
            return ResponseEntity.ok("获取模板列表成功");
        } catch (Exception e) {
            log.error("获取模板列表失败", e);
            return ResponseEntity.badRequest().body("获取模板列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据模板创建合成任务
     */
    @PostMapping("/create-from-template")
    public ResponseEntity<VideoCompositionResponse> createFromTemplate(@RequestBody CreateFromTemplateRequest request) {
        try {
            log.info("根据模板创建合成任务: {}", request.getTemplateId());
            // TODO: 实现基于模板的合成任务创建逻辑
            return ResponseEntity.ok(new VideoCompositionResponse().setStatus("pending"));
        } catch (Exception e) {
            log.error("根据模板创建合成任务失败", e);
            return ResponseEntity.badRequest().body(
                    new VideoCompositionResponse()
                            .setStatus("failed")
                            .setErrorMessage(e.getMessage())
            );
        }
    }
}
